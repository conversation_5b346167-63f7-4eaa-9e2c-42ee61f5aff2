/* Copyright 2024 PDF.js Editor Client
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * PDF编辑器客户端类，用于父页面与PDF.js viewer iframe之间的通信
 * 提供简单易用的API来控制PDF编辑功能
 */
class PDFEditorClient {
  #iframe = null;
  #isReady = false;
  #messageId = 0;
  #pendingMessages = new Map();
  #eventListeners = new Map();
  #supportedModes = [];
  #currentMode = null;

  /**
   * 构造函数
   * @param {HTMLIFrameElement} iframe - PDF.js viewer的iframe元素
   */
  constructor(iframe) {
    if (!iframe || iframe.tagName !== 'IFRAME') {
      throw new Error('Invalid iframe element provided');
    }

    this.#iframe = iframe;
    this.#setupMessageListener();
    this.#waitForInitialization();
  }

  /**
   * 设置消息监听器
   */
  #setupMessageListener() {
    window.addEventListener('message', (event) => {
      if (event.source !== this.#iframe.contentWindow) {
        return;
      }

      const { type, data, id } = event.data;

      // 处理响应消息
      if (type.endsWith('-response') || type.endsWith('-error')) {
        const pendingMessage = this.#pendingMessages.get(id);
        if (pendingMessage) {
          this.#pendingMessages.delete(id);
          if (type.endsWith('-error')) {
            pendingMessage.reject(new Error(data.error));
          } else {
            pendingMessage.resolve(data);
          }
        }
        return;
      }

      // 处理事件消息
      this.#handleEvent(type, data);
    });
  }

  /**
   * 等待编辑器初始化
   */
  #waitForInitialization() {
    this.on('editor-initialized', (data) => {
      this.#isReady = true;
      this.#supportedModes = data.supportedModes;
      this.#currentMode = data.currentMode;
    });
  }

  /**
   * 处理事件消息
   */
  #handleEvent(type, data) {
    const listeners = this.#eventListeners.get(type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${type}:`, error);
        }
      });
    }
  }

  /**
   * 发送消息到iframe
   */
  #sendMessage(type, data = {}) {
    return new Promise((resolve, reject) => {
      if (!this.#iframe.contentWindow) {
        reject(new Error('Iframe not available'));
        return;
      }

      const id = ++this.#messageId;
      this.#pendingMessages.set(id, { resolve, reject });

      this.#iframe.contentWindow.postMessage({
        type,
        data,
        id
      }, '*');

      // 设置超时
      setTimeout(() => {
        if (this.#pendingMessages.has(id)) {
          this.#pendingMessages.delete(id);
          reject(new Error(`Message timeout: ${type}`));
        }
      }, 10000);
    });
  }

  /**
   * 等待编辑器准备就绪
   */
  async waitForReady() {
    if (this.#isReady) {
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      this.once('editor-initialized', () => {
        resolve();
      });
    });
  }

  /**
   * 添加事件监听器
   */
  on(eventType, callback) {
    if (!this.#eventListeners.has(eventType)) {
      this.#eventListeners.set(eventType, []);
    }
    this.#eventListeners.get(eventType).push(callback);
  }

  /**
   * 添加一次性事件监听器
   */
  once(eventType, callback) {
    const onceCallback = (data) => {
      callback(data);
      this.off(eventType, onceCallback);
    };
    this.on(eventType, onceCallback);
  }

  /**
   * 移除事件监听器
   */
  off(eventType, callback) {
    const listeners = this.#eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取支持的编辑器模式
   */
  getSupportedModes() {
    return this.#supportedModes;
  }

  /**
   * 获取当前编辑器模式
   */
  getCurrentMode() {
    return this.#currentMode;
  }

  /**
   * 切换编辑器模式
   */
  async switchEditorMode(mode, options = {}) {
    const result = await this.#sendMessage('switch-editor-mode', {
      mode,
      ...options
    });
    this.#currentMode = result.currentMode;
    return result;
  }

  /**
   * 添加文本注释
   */
  async addTextAnnotation(options) {
    const { x, y, pageIndex, text, fontSize = 12, color = [0, 0, 0] } = options;
    return await this.#sendMessage('add-text-annotation', {
      x, y, pageIndex, text, fontSize, color
    });
  }

  /**
   * 添加绘图注释
   */
  async addInkAnnotation(options) {
    const { pageIndex, paths, color = [0, 0, 0], thickness = 2, opacity = 1 } = options;
    return await this.#sendMessage('add-ink-annotation', {
      pageIndex, paths, color, thickness, opacity
    });
  }

  /**
   * 添加高亮注释
   */
  async addHighlightAnnotation(options) {
    const { pageIndex, quadPoints, color = [255, 255, 0], opacity = 0.3 } = options;
    return await this.#sendMessage('add-highlight-annotation', {
      pageIndex, quadPoints, color, opacity
    });
  }

  /**
   * 添加图章注释
   */
  async addStampAnnotation(options) {
    const { x, y, pageIndex, imageUrl, width = 100, height = 100 } = options;
    return await this.#sendMessage('add-stamp-annotation', {
      x, y, pageIndex, imageUrl, width, height
    });
  }

  /**
   * 删除注释
   */
  async removeAnnotation(editorId) {
    return await this.#sendMessage('remove-annotation', { id: editorId });
  }

  /**
   * 获取所有注释
   */
  async getAllAnnotations() {
    return await this.#sendMessage('get-all-annotations');
  }

  /**
   * 撤销操作
   */
  async undo() {
    return await this.#sendMessage('undo');
  }

  /**
   * 重做操作
   */
  async redo() {
    return await this.#sendMessage('redo');
  }

  /**
   * 保存PDF
   */
  async savePDF() {
    const result = await this.#sendMessage('save-pdf');
    // 将数组转换为Uint8Array
    if (result.data) {
      result.data = new Uint8Array(result.data);
    }
    return result;
  }

  /**
   * 设置编辑器属性
   */
  async setEditorProperties(editorId, properties) {
    return await this.#sendMessage('set-editor-properties', {
      editorId,
      properties
    });
  }

  /**
   * 启用文本编辑模式
   */
  async enableTextMode() {
    return await this.switchEditorMode(3); // AnnotationEditorType.FREETEXT
  }

  /**
   * 启用绘图模式
   */
  async enableInkMode() {
    return await this.switchEditorMode(15); // AnnotationEditorType.INK
  }

  /**
   * 启用高亮模式
   */
  async enableHighlightMode() {
    return await this.switchEditorMode(9); // AnnotationEditorType.HIGHLIGHT
  }

  /**
   * 启用图章模式
   */
  async enableStampMode() {
    return await this.switchEditorMode(13); // AnnotationEditorType.STAMP
  }

  /**
   * 禁用编辑模式
   */
  async disableEditMode() {
    return await this.switchEditorMode(0); // AnnotationEditorType.NONE
  }

  /**
   * 销毁客户端
   */
  destroy() {
    this.#pendingMessages.clear();
    this.#eventListeners.clear();
    this.#iframe = null;
    this.#isReady = false;
  }
}

// 工厂函数，用于创建PDF编辑器客户端
function createPDFEditorClient(iframe) {
  return new PDFEditorClient(iframe);
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { PDFEditorClient, createPDFEditorClient };
} else if (typeof window !== 'undefined') {
  window.PDFEditorClient = PDFEditorClient;
  window.createPDFEditorClient = createPDFEditorClient;
}

export { PDFEditorClient, createPDFEditorClient };
