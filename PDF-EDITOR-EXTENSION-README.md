# PDF.js 编辑器扩展

基于 PDF.js 的在线 PDF 编辑功能扩展，支持通过 iframe 进行跨域通信和编辑操作。

## 🚀 特性

- **完整的编辑功能**: 支持文本、绘图、高亮、图章、签名等多种注释类型
- **iframe 集成**: 完美支持 iframe 嵌入，提供跨域通信机制
- **缩放自适应**: 智能处理页面缩放，确保编辑功能在不同缩放级别下正常工作
- **坐标转换**: 提供屏幕坐标与 PDF 坐标的双向转换
- **撤销重做**: 完整的操作历史管理
- **事件驱动**: 丰富的事件系统，实时响应编辑状态变化
- **易于集成**: 简单的 API 设计，快速集成到现有项目
- **🆕 修改建议系统**: 支持外部系统提供修改建议，用户一键接受后PDF立即显示修改效果
- **🆕 批量操作**: 支持批量应用、预览和管理修改建议
- **🆕 智能分类**: 按类型、优先级、页面等维度管理建议

## 📦 项目结构

```
pdf.js/
├── src/
│   ├── pdf-editor-extension.js      # 编辑器扩展核心模块
│   ├── pdf-editor-client.js         # 客户端通信库
│   ├── pdf-editor-scale-adapter.js  # 缩放适配器
│   └── pdf-modification-suggestions.js # 修改建议管理器
├── web/
│   └── viewer.js                     # 修改后的查看器（集成扩展）
├── examples/
│   ├── pdf-editor-iframe-example.html # 完整使用示例
│   ├── pdf-modification-suggestions-example.html # 修改建议系统示例
│   └── pdf-suggestions-api-demo.html  # API演示示例
├── docs/
│   ├── PDF-EDITOR-API.md            # API 文档
│   └── MODIFICATION-SUGGESTIONS-GUIDE.md # 修改建议系统使用指南
└── gulpfile.mjs                     # 修改后的构建配置
```

## 🛠️ 安装和构建

### 1. 克隆项目

```bash
git clone https://github.com/mozilla/pdf.js.git
cd pdf.js
```

### 2. 安装依赖

```bash
npm install
```

### 3. 构建项目

```bash
# 构建通用版本（包含编辑器扩展）
npm run gulp generic

# 构建完成后，文件将位于 build/generic/ 目录
```

### 4. 启动本地服务器

```bash
# 使用 Python
python -m http.server 8000

# 或使用 Node.js
npx http-server -p 8000

# 访问示例页面
# http://localhost:8000/examples/pdf-editor-iframe-example.html
```

## 🎯 快速开始

### 基本使用

```html
<!DOCTYPE html>
<html>
<head>
    <title>PDF编辑器</title>
</head>
<body>
    <iframe id="pdfViewer" 
            src="build/generic/web/viewer.html?file=your-document.pdf"
            width="100%" height="600px">
    </iframe>

    <script type="module">
        import { createPDFEditorClient } from 'build/generic/src/pdf-editor-client.js';

        const iframe = document.getElementById('pdfViewer');
        const pdfEditor = createPDFEditorClient(iframe);

        // 等待编辑器准备就绪
        await pdfEditor.waitForReady();

        // 启用文本编辑模式
        await pdfEditor.enableTextMode();

        // 添加文本注释
        await pdfEditor.addTextAnnotation({
            x: 100,
            y: 100,
            pageIndex: 0,
            text: 'Hello World!',
            fontSize: 14,
            color: [0, 0, 0]
        });

        // 保存PDF
        const result = await pdfEditor.savePDF();
        const blob = new Blob([result.data], { type: 'application/pdf' });
        // 创建下载链接...
    </script>
</body>
</html>
```

### 高级功能

```javascript
// 监听编辑器事件
pdfEditor.on('editor-mode-changed', (data) => {
    console.log('编辑模式变化:', data.mode);
});

pdfEditor.on('scale-changing', (data) => {
    console.log('缩放变化:', data.scale);
});

// 添加不同类型的注释
await pdfEditor.addInkAnnotation({
    pageIndex: 0,
    paths: [
        [{x: 10, y: 10}, {x: 20, y: 20}, {x: 30, y: 15}]
    ],
    color: [255, 0, 0],
    thickness: 3
});

await pdfEditor.addHighlightAnnotation({
    pageIndex: 0,
    quadPoints: [[100, 100, 200, 100, 200, 120, 100, 120]],
    color: [255, 255, 0],
    opacity: 0.3
});

// 操作控制
await pdfEditor.undo();
await pdfEditor.redo();

// 获取所有注释
const annotations = await pdfEditor.getAllAnnotations();
console.log('注释列表:', annotations);

// 🆕 修改建议系统 - 核心功能
// 添加修改建议
const suggestion = {
    type: 'text-replace',
    pageIndex: 0,
    position: { x: 100, y: 200, width: 150, height: 20 },
    originalText: '原始文本',
    newText: '修改后的文本',
    description: '将原始文本替换为修改后的文本',
    priority: 'high',
    category: 'review'
};

await pdfEditor.addSuggestion(suggestion);

// 用户点击"接受"按钮，PDF立即显示修改效果
await pdfEditor.applySuggestion(suggestionId);

// 批量应用页面所有建议
await pdfEditor.applyPageSuggestions(0);
```

## 📚 API 文档

详细的 API 文档请参考 [PDF-EDITOR-API.md](docs/PDF-EDITOR-API.md)

### 主要 API

- **编辑模式控制**: `enableTextMode()`, `enableInkMode()`, `enableHighlightMode()` 等
- **注释管理**: `addTextAnnotation()`, `addInkAnnotation()`, `removeAnnotation()` 等
- **操作控制**: `undo()`, `redo()`, `savePDF()`
- **事件监听**: `on()`, `once()`, `off()`
- **坐标转换**: `screenToPDFCoordinates()`, `pdfToScreenCoordinates()`

## 🎨 编辑器模式

| 模式 | 常量 | 描述 |
|------|------|------|
| 无编辑 | `NONE (0)` | 禁用所有编辑功能 |
| 文本编辑 | `FREETEXT (3)` | 添加和编辑文本注释 |
| 高亮 | `HIGHLIGHT (9)` | 高亮选中的文本 |
| 图章 | `STAMP (13)` | 添加图片图章 |
| 绘图 | `INK (15)` | 自由绘图和墨迹注释 |
| 签名 | `SIGNATURE (101)` | 数字签名 |

## 🔧 技术架构

### 核心组件

1. **PDFEditorExtension**: 编辑器扩展核心，负责与 PDF.js 的 AnnotationEditorUIManager 交互
2. **PDFEditorClient**: 客户端通信库，提供简单易用的 API 接口
3. **PDFEditorScaleAdapter**: 缩放适配器，处理不同缩放级别下的坐标转换和 UI 适配

### 通信机制

- 使用 `postMessage` API 实现 iframe 跨域通信
- 支持异步消息处理和错误处理
- 提供事件驱动的状态同步

### 缩放适配

- 自动检测页面缩放变化
- 智能调整编辑器元素位置和大小
- 提供坐标系转换功能
- 确保编辑功能在任何缩放级别下都能正常工作

## 🌐 浏览器兼容性

- Chrome 110+
- Firefox ESR
- Safari 16.4+
- Edge 110+

## 📝 使用注意事项

1. **iframe 路径**: 确保 iframe 的 src 指向正确的 viewer.html 路径
2. **初始化时机**: 编辑器扩展需要在 iframe 加载完成后初始化
3. **跨域配置**: 如果涉及跨域，需要正确配置 CORS 策略
4. **PDF 保存**: 保存的 PDF 包含所有编辑内容，支持标准 PDF 阅读器打开
5. **内存管理**: 使用完毕后调用 `destroy()` 方法释放资源

## 🚀 部署建议

### 生产环境

1. 使用 CDN 托管静态资源
2. 启用 gzip 压缩
3. 配置适当的缓存策略
4. 考虑使用 Web Workers 提升性能

### 安全考虑

1. 验证上传的 PDF 文件
2. 限制编辑操作的权限
3. 对保存的 PDF 进行安全检查
4. 实施适当的 CSP 策略

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目基于 Apache License 2.0 许可证开源。

## 🔗 相关链接

- [PDF.js 官方文档](https://mozilla.github.io/pdf.js/)
- [PDF.js GitHub 仓库](https://github.com/mozilla/pdf.js)
- [API 详细文档](docs/PDF-EDITOR-API.md)
- [修改建议系统使用指南](docs/MODIFICATION-SUGGESTIONS-GUIDE.md)
- [基础编辑示例](examples/pdf-editor-iframe-example.html)
- [修改建议系统示例](examples/pdf-modification-suggestions-example.html)
- [API演示示例](examples/pdf-suggestions-api-demo.html)
