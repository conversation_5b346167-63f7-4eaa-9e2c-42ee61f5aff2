/* Copyright 2024 PDF.js Editor Scale Adapter
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * PDF编辑器缩放适配器
 * 处理不同缩放级别下的坐标转换和UI元素适配
 */
class PDFEditorScaleAdapter {
  #pdfViewer = null;
  #eventBus = null;
  #currentScale = 1;
  #baseScale = 1;
  #scaleChangeCallbacks = new Set();

  constructor(pdfViewer, eventBus) {
    this.#pdfViewer = pdfViewer;
    this.#eventBus = eventBus;
    this.#currentScale = pdfViewer.currentScale || 1;
    this.#baseScale = this.#currentScale;
    
    this.#setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  #setupEventListeners() {
    // 监听缩放变化事件
    this.#eventBus.on("scalechanging", (event) => {
      const previousScale = this.#currentScale;
      this.#currentScale = event.scale;
      
      this.#handleScaleChange(previousScale, this.#currentScale);
    });

    // 监听页面渲染完成事件
    this.#eventBus.on("pagerendered", (event) => {
      this.#updatePageScale(event.pageNumber);
    });
  }

  /**
   * 处理缩放变化
   */
  #handleScaleChange(previousScale, newScale) {
    const scaleRatio = newScale / previousScale;
    
    // 通知所有注册的回调函数
    this.#scaleChangeCallbacks.forEach(callback => {
      try {
        callback({
          previousScale,
          newScale,
          scaleRatio,
          baseScale: this.#baseScale
        });
      } catch (error) {
        console.error("Error in scale change callback:", error);
      }
    });

    // 更新编辑器元素的缩放
    this.#updateEditorsScale(scaleRatio);
  }

  /**
   * 更新编辑器元素的缩放
   */
  #updateEditorsScale(scaleRatio) {
    const annotationEditorUIManager = this.#pdfViewer.annotationEditorUIManager;
    if (!annotationEditorUIManager) {
      return;
    }

    // 获取所有编辑器并更新其缩放
    for (const editor of annotationEditorUIManager.getEditors()) {
      this.#updateEditorScale(editor, scaleRatio);
    }
  }

  /**
   * 更新单个编辑器的缩放
   */
  #updateEditorScale(editor, scaleRatio) {
    if (!editor || !editor.div) {
      return;
    }

    // 更新编辑器的位置和大小
    const rect = editor.div.getBoundingClientRect();
    const parentRect = editor.parent.div.getBoundingClientRect();

    // 计算相对位置
    const relativeX = (rect.left - parentRect.left) * scaleRatio;
    const relativeY = (rect.top - parentRect.top) * scaleRatio;

    // 更新编辑器位置
    editor.div.style.left = `${relativeX}px`;
    editor.div.style.top = `${relativeY}px`;

    // 更新编辑器大小（如果需要）
    if (editor.div.style.width) {
      const currentWidth = parseFloat(editor.div.style.width);
      editor.div.style.width = `${currentWidth * scaleRatio}px`;
    }

    if (editor.div.style.height) {
      const currentHeight = parseFloat(editor.div.style.height);
      editor.div.style.height = `${currentHeight * scaleRatio}px`;
    }

    // 更新字体大小（对于文本编辑器）
    if (editor.constructor._editorType === 3) { // FREETEXT
      this.#updateTextEditorFontSize(editor, scaleRatio);
    }

    // 更新线条粗细（对于绘图编辑器）
    if (editor.constructor._editorType === 15) { // INK
      this.#updateInkEditorStrokeWidth(editor, scaleRatio);
    }
  }

  /**
   * 更新文本编辑器的字体大小
   */
  #updateTextEditorFontSize(editor, scaleRatio) {
    if (editor.editorDiv) {
      const currentFontSize = parseFloat(
        window.getComputedStyle(editor.editorDiv).fontSize
      );
      editor.editorDiv.style.fontSize = `${currentFontSize * scaleRatio}px`;
    }
  }

  /**
   * 更新绘图编辑器的线条粗细
   */
  #updateInkEditorStrokeWidth(editor, scaleRatio) {
    if (editor.canvas) {
      const ctx = editor.canvas.getContext('2d');
      if (ctx) {
        ctx.lineWidth = (ctx.lineWidth || 1) * scaleRatio;
      }
    }
  }

  /**
   * 更新页面缩放
   */
  #updatePageScale(pageNumber) {
    const pageView = this.#pdfViewer.getPageView(pageNumber - 1);
    if (!pageView) {
      return;
    }

    const annotationEditorLayer = pageView.annotationEditorLayer;
    if (!annotationEditorLayer) {
      return;
    }

    // 确保编辑器层的缩放与页面一致
    const viewport = pageView.viewport;
    const scale = viewport.scale;
    
    if (annotationEditorLayer.div) {
      annotationEditorLayer.div.style.transform = `scale(${scale})`;
      annotationEditorLayer.div.style.transformOrigin = '0 0';
    }
  }

  /**
   * 坐标转换：从屏幕坐标转换为PDF坐标
   */
  screenToPDFCoordinates(screenX, screenY, pageNumber) {
    const pageView = this.#pdfViewer.getPageView(pageNumber - 1);
    if (!pageView) {
      return { x: screenX, y: screenY };
    }

    const pageRect = pageView.div.getBoundingClientRect();
    const viewport = pageView.viewport;

    // 转换为页面相对坐标
    const pageX = screenX - pageRect.left;
    const pageY = screenY - pageRect.top;

    // 转换为PDF坐标系
    const pdfX = pageX / viewport.scale;
    const pdfY = (pageRect.height - pageY) / viewport.scale;

    return { x: pdfX, y: pdfY };
  }

  /**
   * 坐标转换：从PDF坐标转换为屏幕坐标
   */
  pdfToScreenCoordinates(pdfX, pdfY, pageNumber) {
    const pageView = this.#pdfViewer.getPageView(pageNumber - 1);
    if (!pageView) {
      return { x: pdfX, y: pdfY };
    }

    const pageRect = pageView.div.getBoundingClientRect();
    const viewport = pageView.viewport;

    // 转换为页面坐标
    const pageX = pdfX * viewport.scale;
    const pageY = (viewport.height - pdfY) * viewport.scale;

    // 转换为屏幕坐标
    const screenX = pageX + pageRect.left;
    const screenY = pageY + pageRect.top;

    return { x: screenX, y: screenY };
  }

  /**
   * 获取页面的缩放比例
   */
  getPageScale(pageNumber) {
    const pageView = this.#pdfViewer.getPageView(pageNumber - 1);
    return pageView ? pageView.viewport.scale : this.#currentScale;
  }

  /**
   * 获取当前缩放比例
   */
  getCurrentScale() {
    return this.#currentScale;
  }

  /**
   * 获取基础缩放比例
   */
  getBaseScale() {
    return this.#baseScale;
  }

  /**
   * 注册缩放变化回调
   */
  onScaleChange(callback) {
    this.#scaleChangeCallbacks.add(callback);
  }

  /**
   * 取消注册缩放变化回调
   */
  offScaleChange(callback) {
    this.#scaleChangeCallbacks.delete(callback);
  }

  /**
   * 计算缩放后的尺寸
   */
  scaleSize(size, scale = this.#currentScale) {
    return size * scale;
  }

  /**
   * 计算缩放后的位置
   */
  scalePosition(position, scale = this.#currentScale) {
    return {
      x: position.x * scale,
      y: position.y * scale
    };
  }

  /**
   * 获取页面的可视区域
   */
  getPageVisibleArea(pageNumber) {
    const pageView = this.#pdfViewer.getPageView(pageNumber - 1);
    if (!pageView) {
      return null;
    }

    const pageRect = pageView.div.getBoundingClientRect();
    const containerRect = this.#pdfViewer.container.getBoundingClientRect();

    return {
      left: Math.max(0, containerRect.left - pageRect.left),
      top: Math.max(0, containerRect.top - pageRect.top),
      right: Math.min(pageRect.width, containerRect.right - pageRect.left),
      bottom: Math.min(pageRect.height, containerRect.bottom - pageRect.top),
      width: pageRect.width,
      height: pageRect.height
    };
  }

  /**
   * 检查点是否在页面可视区域内
   */
  isPointInVisibleArea(x, y, pageNumber) {
    const visibleArea = this.getPageVisibleArea(pageNumber);
    if (!visibleArea) {
      return false;
    }

    return x >= visibleArea.left && 
           x <= visibleArea.right && 
           y >= visibleArea.top && 
           y <= visibleArea.bottom;
  }

  /**
   * 销毁适配器
   */
  destroy() {
    this.#scaleChangeCallbacks.clear();
    this.#pdfViewer = null;
    this.#eventBus = null;
  }
}

export { PDFEditorScaleAdapter };
