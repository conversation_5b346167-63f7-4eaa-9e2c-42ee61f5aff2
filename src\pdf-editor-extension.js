/* Copyright 2024 PDF.js Editor Extension
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { AnnotationEditorType } from "./shared/util.js";
import { PDFEditorScaleAdapter } from "./pdf-editor-scale-adapter.js";
import { PDFModificationSuggestions } from "./pdf-modification-suggestions.js";

/**
 * PDF编辑器扩展类，提供丰富的编辑API和功能接口
 * 支持通过iframe进行跨域通信和编辑操作
 */
class PDFEditorExtension {
  #uiManager = null;
  #pdfViewer = null;
  #eventBus = null;
  #isInitialized = false;
  #messageHandlers = new Map();
  #editorCallbacks = new Map();
  #scaleAdapter = null;
  #suggestionManager = null;

  constructor() {
    this.#setupMessageListener();
  }

  /**
   * 初始化编辑器扩展
   * @param {Object} options - 初始化选项
   * @param {AnnotationEditorUIManager} options.uiManager - UI管理器
   * @param {PDFViewer} options.pdfViewer - PDF查看器
   * @param {EventBus} options.eventBus - 事件总线
   */
  initialize({ uiManager, pdfViewer, eventBus }) {
    if (this.#isInitialized) {
      console.warn("PDFEditorExtension already initialized");
      return;
    }

    this.#uiManager = uiManager;
    this.#pdfViewer = pdfViewer;
    this.#eventBus = eventBus;
    this.#isInitialized = true;

    // 初始化缩放适配器
    this.#scaleAdapter = new PDFEditorScaleAdapter(pdfViewer, eventBus);

    // 初始化修改建议管理器
    this.#suggestionManager = new PDFModificationSuggestions(this);

    this.#setupEventListeners();
    this.#registerMessageHandlers();

    // 通知父窗口编辑器已初始化
    this.#postMessage({
      type: "editor-initialized",
      data: {
        supportedModes: this.getSupportedEditorModes(),
        currentMode: this.getCurrentMode(),
      },
    });
  }

  /**
   * 设置消息监听器，用于iframe通信
   */
  #setupMessageListener() {
    window.addEventListener("message", (event) => {
      if (!this.#isInitialized) {
        return;
      }

      const { type, data, id } = event.data;
      const handler = this.#messageHandlers.get(type);

      if (handler) {
        try {
          const result = handler(data);
          if (result instanceof Promise) {
            result
              .then((response) => {
                this.#postMessage({
                  type: `${type}-response`,
                  data: response,
                  id,
                });
              })
              .catch((error) => {
                this.#postMessage({
                  type: `${type}-error`,
                  data: { error: error.message },
                  id,
                });
              });
          } else {
            this.#postMessage({
              type: `${type}-response`,
              data: result,
              id,
            });
          }
        } catch (error) {
          this.#postMessage({
            type: `${type}-error`,
            data: { error: error.message },
            id,
          });
        }
      }
    });
  }

  /**
   * 设置事件监听器
   */
  #setupEventListeners() {
    // 监听编辑器模式变化
    this.#eventBus.on("annotationeditormodechanged", (event) => {
      this.#postMessage({
        type: "editor-mode-changed",
        data: {
          mode: event.mode,
          previousMode: this.#previousMode,
        },
      });
      this.#previousMode = event.mode;
    });

    // 监听编辑器状态变化
    this.#eventBus.on("annotationeditorstateschanged", (event) => {
      this.#postMessage({
        type: "editor-states-changed",
        data: event.details,
      });
    });

    // 监听页面缩放变化
    this.#eventBus.on("scalechanging", (event) => {
      this.#postMessage({
        type: "scale-changing",
        data: {
          scale: event.scale,
          presetValue: event.presetValue,
        },
      });
    });
  }

  /**
   * 注册消息处理器
   */
  #registerMessageHandlers() {
    // 切换编辑器模式
    this.#messageHandlers.set("switch-editor-mode", async (data) => {
      const { mode, editId, isFromKeyboard, mustEnterInEditMode, editComment } = data;
      await this.#uiManager.updateMode(
        mode,
        editId,
        isFromKeyboard,
        mustEnterInEditMode,
        editComment
      );
      return { success: true, currentMode: mode };
    });

    // 获取当前编辑器模式
    this.#messageHandlers.set("get-current-mode", () => {
      return { mode: this.getCurrentMode() };
    });

    // 获取支持的编辑器模式
    this.#messageHandlers.set("get-supported-modes", () => {
      return { modes: this.getSupportedEditorModes() };
    });

    // 添加文本注释
    this.#messageHandlers.set("add-text-annotation", (data) => {
      return this.addTextAnnotation(data);
    });

    // 添加绘图注释
    this.#messageHandlers.set("add-ink-annotation", (data) => {
      return this.addInkAnnotation(data);
    });

    // 添加高亮注释
    this.#messageHandlers.set("add-highlight-annotation", (data) => {
      return this.addHighlightAnnotation(data);
    });

    // 添加图章注释
    this.#messageHandlers.set("add-stamp-annotation", (data) => {
      return this.addStampAnnotation(data);
    });

    // 删除注释
    this.#messageHandlers.set("remove-annotation", (data) => {
      return this.removeAnnotation(data.id);
    });

    // 获取所有注释
    this.#messageHandlers.set("get-all-annotations", () => {
      return this.getAllAnnotations();
    });

    // 撤销操作
    this.#messageHandlers.set("undo", () => {
      this.#uiManager.undo();
      return { success: true };
    });

    // 重做操作
    this.#messageHandlers.set("redo", () => {
      this.#uiManager.redo();
      return { success: true };
    });

    // 保存PDF
    this.#messageHandlers.set("save-pdf", async () => {
      return await this.savePDF();
    });

    // 设置编辑器属性
    this.#messageHandlers.set("set-editor-properties", (data) => {
      return this.setEditorProperties(data);
    });

    // 坐标转换
    this.#messageHandlers.set("screen-to-pdf-coordinates", (data) => {
      const { screenX, screenY, pageNumber } = data;
      return this.#scaleAdapter.screenToPDFCoordinates(screenX, screenY, pageNumber);
    });

    this.#messageHandlers.set("pdf-to-screen-coordinates", (data) => {
      const { pdfX, pdfY, pageNumber } = data;
      return this.#scaleAdapter.pdfToScreenCoordinates(pdfX, pdfY, pageNumber);
    });

    // 获取缩放信息
    this.#messageHandlers.set("get-scale-info", () => {
      return {
        currentScale: this.#scaleAdapter.getCurrentScale(),
        baseScale: this.#scaleAdapter.getBaseScale(),
      };
    });

    // 修改建议管理
    this.#messageHandlers.set("add-suggestion", (data) => {
      return { suggestionId: this.#suggestionManager.addSuggestion(data) };
    });

    this.#messageHandlers.set("add-suggestions", (data) => {
      return { suggestionIds: this.#suggestionManager.addSuggestions(data.suggestions) };
    });

    this.#messageHandlers.set("apply-suggestion", async (data) => {
      return await this.#suggestionManager.applySuggestion(data.suggestionId);
    });

    this.#messageHandlers.set("apply-suggestions", async (data) => {
      return await this.#suggestionManager.applySuggestions(data.suggestionIds);
    });

    this.#messageHandlers.set("apply-page-suggestions", async (data) => {
      return await this.#suggestionManager.applyPageSuggestions(data.pageIndex);
    });

    this.#messageHandlers.set("get-all-suggestions", () => {
      return { suggestions: this.#suggestionManager.getAllSuggestions() };
    });

    this.#messageHandlers.set("get-page-suggestions", (data) => {
      return {
        suggestions: this.#suggestionManager.getSuggestionsByPage(data.pageIndex)
      };
    });

    this.#messageHandlers.set("preview-suggestion", async (data) => {
      await this.#suggestionManager.previewSuggestion(data.suggestionId);
      return { success: true };
    });

    this.#messageHandlers.set("clear-preview", (data) => {
      this.#suggestionManager.clearPreview(data.suggestionId);
      return { success: true };
    });

    this.#messageHandlers.set("reject-suggestion", (data) => {
      this.#suggestionManager.rejectSuggestion(data.suggestionId);
      return { success: true };
    });

    this.#messageHandlers.set("get-suggestions-statistics", () => {
      return this.#suggestionManager.getStatistics();
    });
  }

  /**
   * 发送消息到父窗口
   */
  #postMessage(message) {
    if (window.parent !== window) {
      window.parent.postMessage(message, "*");
    }
  }

  /**
   * 获取支持的编辑器模式
   */
  getSupportedEditorModes() {
    return [
      { type: AnnotationEditorType.NONE, name: "none", label: "无编辑" },
      { type: AnnotationEditorType.FREETEXT, name: "freetext", label: "文本" },
      { type: AnnotationEditorType.INK, name: "ink", label: "绘图" },
      { type: AnnotationEditorType.HIGHLIGHT, name: "highlight", label: "高亮" },
      { type: AnnotationEditorType.STAMP, name: "stamp", label: "图章" },
      { type: AnnotationEditorType.SIGNATURE, name: "signature", label: "签名" },
    ];
  }

  /**
   * 获取当前编辑器模式
   */
  getCurrentMode() {
    return this.#uiManager?.getMode() || AnnotationEditorType.NONE;
  }

  /**
   * 添加文本注释
   */
  addTextAnnotation({ x, y, pageIndex, text, fontSize, color, useScreenCoordinates = false }) {
    const layer = this.#uiManager.getLayer(pageIndex);
    if (!layer) {
      throw new Error(`Page ${pageIndex} not found`);
    }

    // 如果使用屏幕坐标，转换为PDF坐标
    let pdfCoords = { x, y };
    if (useScreenCoordinates) {
      pdfCoords = this.#scaleAdapter.screenToPDFCoordinates(x, y, pageIndex + 1);
    }

    // 切换到文本编辑模式
    this.#uiManager.updateMode(AnnotationEditorType.FREETEXT);

    // 创建文本编辑器
    const editor = layer.createAndAddNewEditor(
      { offsetX: pdfCoords.x, offsetY: pdfCoords.y },
      false,
      {
        content: text,
        fontSize: (fontSize || 12) * this.#scaleAdapter.getCurrentScale(),
        color: color || [0, 0, 0],
      }
    );

    return {
      success: true,
      editorId: editor?.id,
      annotationId: editor?.annotationElementId,
    };
  }

  /**
   * 添加墨迹注释
   */
  addInkAnnotation({ pageIndex, paths, color, thickness, opacity }) {
    const layer = this.#uiManager.getLayer(pageIndex);
    if (!layer) {
      throw new Error(`Page ${pageIndex} not found`);
    }

    this.#uiManager.updateMode(AnnotationEditorType.INK);

    const editor = layer.createAndAddNewEditor(
      { offsetX: 0, offsetY: 0 },
      false,
      {
        paths,
        color: color || [0, 0, 0],
        thickness: thickness || 2,
        opacity: opacity || 1,
      }
    );

    return {
      success: true,
      editorId: editor?.id,
      annotationId: editor?.annotationElementId,
    };
  }

  /**
   * 添加高亮注释
   */
  addHighlightAnnotation({ pageIndex, quadPoints, color, opacity }) {
    const layer = this.#uiManager.getLayer(pageIndex);
    if (!layer) {
      throw new Error(`Page ${pageIndex} not found`);
    }

    this.#uiManager.updateMode(AnnotationEditorType.HIGHLIGHT);

    const editor = layer.createAndAddNewEditor(
      { offsetX: 0, offsetY: 0 },
      false,
      {
        quadPoints,
        color: color || [255, 255, 0],
        opacity: opacity || 0.3,
      }
    );

    return {
      success: true,
      editorId: editor?.id,
      annotationId: editor?.annotationElementId,
    };
  }

  /**
   * 添加图章注释
   */
  addStampAnnotation({ x, y, pageIndex, imageUrl, width, height }) {
    const layer = this.#uiManager.getLayer(pageIndex);
    if (!layer) {
      throw new Error(`Page ${pageIndex} not found`);
    }

    this.#uiManager.updateMode(AnnotationEditorType.STAMP);

    const editor = layer.createAndAddNewEditor(
      { offsetX: x, offsetY: y },
      false,
      {
        bitmapUrl: imageUrl,
        width: width || 100,
        height: height || 100,
      }
    );

    return {
      success: true,
      editorId: editor?.id,
      annotationId: editor?.annotationElementId,
    };
  }

  /**
   * 删除注释
   */
  removeAnnotation(editorId) {
    const editor = this.#uiManager.getEditor(editorId);
    if (!editor) {
      throw new Error(`Editor ${editorId} not found`);
    }

    editor.remove();
    return { success: true };
  }

  /**
   * 获取所有注释
   */
  getAllAnnotations() {
    const annotations = [];
    for (const editor of this.#uiManager.getEditors()) {
      annotations.push({
        id: editor.id,
        type: editor.constructor._editorType,
        pageIndex: editor.pageIndex,
        data: editor.serialize(),
      });
    }
    return { annotations };
  }

  /**
   * 保存PDF
   */
  async savePDF() {
    try {
      const pdfDocument = this.#pdfViewer.pdfDocument;
      const data = await pdfDocument.saveDocument();
      return {
        success: true,
        data: Array.from(data),
      };
    } catch (error) {
      throw new Error(`Failed to save PDF: ${error.message}`);
    }
  }

  /**
   * 设置编辑器属性
   */
  setEditorProperties({ editorId, properties }) {
    const editor = this.#uiManager.getEditor(editorId);
    if (!editor) {
      throw new Error(`Editor ${editorId} not found`);
    }

    // 根据编辑器类型设置不同的属性
    Object.entries(properties).forEach(([key, value]) => {
      if (editor[key] !== undefined) {
        editor[key] = value;
      }
    });

    editor.rebuild();
    return { success: true };
  }
}

// 全局实例
const pdfEditorExtension = new PDFEditorExtension();

export { PDFEditorExtension, pdfEditorExtension };
