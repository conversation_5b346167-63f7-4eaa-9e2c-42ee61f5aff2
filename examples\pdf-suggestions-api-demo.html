<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF修改建议API演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }

        .control-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .pdf-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .section:last-child {
            border-bottom: none;
        }

        .section h3 {
            margin-top: 0;
            color: #2c3e50;
            border-left: 4px solid #3498db;
            padding-left: 10px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-primary:hover { background: #2980b9; }
        .btn-success { background: #27ae60; color: white; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-warning:hover { background: #e67e22; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-danger:hover { background: #c0392b; }

        .suggestion-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }

        .suggestion-preview.applied {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #27ae60; }
        .status-loading { background: #f39c12; }
        .status-error { background: #e74c3c; }

        .pdf-container {
            flex: 1;
            border: none;
        }

        .toolbar {
            background: #34495e;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            height: 150px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin-top: 10px;
        }

        .input-group {
            margin: 10px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input-group input, .input-group textarea, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .input-group textarea {
            height: 60px;
            resize: vertical;
        }

        .suggestion-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .suggestion-item.applied {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .suggestion-info {
            flex: 1;
        }

        .suggestion-actions {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="control-panel">
            <h2>PDF修改建议控制面板</h2>
            
            <!-- 状态显示 -->
            <div class="section">
                <h3>系统状态</h3>
                <div>
                    <span id="statusIndicator" class="status-indicator status-loading"></span>
                    <span id="statusText">正在初始化...</span>
                </div>
            </div>

            <!-- 模拟外部系统建议 -->
            <div class="section">
                <h3>模拟外部系统建议</h3>
                <button class="btn btn-primary" onclick="loadReviewSuggestions()">加载审阅建议</button>
                <button class="btn btn-primary" onclick="loadTranslationSuggestions()">加载翻译建议</button>
                <button class="btn btn-primary" onclick="loadComplianceSuggestions()">加载合规建议</button>
                
                <div class="code-block" id="apiExample">
// API调用示例
const suggestions = [
  {
    type: 'text-replace',
    pageIndex: 0,
    position: { x: 100, y: 200, width: 150, height: 20 },
    originalText: 'old text',
    newText: 'new text',
    description: '修改建议描述',
    priority: 'high'
  }
];

await pdfEditor.addSuggestions(suggestions);
                </div>
            </div>

            <!-- 手动创建建议 -->
            <div class="section">
                <h3>手动创建建议</h3>
                <div class="input-group">
                    <label>建议类型:</label>
                    <select id="suggestionType">
                        <option value="text-replace">替换文本</option>
                        <option value="text-add">添加文本</option>
                        <option value="text-delete">删除文本</option>
                        <option value="highlight">高亮</option>
                        <option value="comment">评论</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>页面索引:</label>
                    <input type="number" id="pageIndex" value="0" min="0">
                </div>
                <div class="input-group">
                    <label>位置 (x,y,width,height):</label>
                    <input type="text" id="position" value="100,200,150,20" placeholder="x,y,width,height">
                </div>
                <div class="input-group">
                    <label>原文本:</label>
                    <input type="text" id="originalText" placeholder="原始文本">
                </div>
                <div class="input-group">
                    <label>新文本:</label>
                    <input type="text" id="newText" placeholder="新文本内容">
                </div>
                <div class="input-group">
                    <label>描述:</label>
                    <textarea id="description" placeholder="修改建议的描述"></textarea>
                </div>
                <button class="btn btn-success" onclick="createCustomSuggestion()">创建建议</button>
            </div>

            <!-- 建议列表 -->
            <div class="section">
                <h3>建议列表</h3>
                <button class="btn btn-warning" onclick="refreshSuggestions()">刷新列表</button>
                <button class="btn btn-success" onclick="applyAllSuggestions()">应用全部</button>
                <div id="suggestionsList"></div>
            </div>

            <!-- 操作日志 -->
            <div class="section">
                <h3>操作日志</h3>
                <div id="logArea" class="log-area"></div>
                <button class="btn btn-warning" onclick="clearLog()">清除日志</button>
            </div>
        </div>

        <div class="pdf-panel">
            <div class="toolbar">
                <div>PDF修改建议演示</div>
                <div id="suggestionStats">建议统计: 加载中...</div>
            </div>
            <iframe id="pdfViewer" class="pdf-container" 
                    src="../web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf">
            </iframe>
        </div>
    </div>

    <script type="module">
        import { createPDFEditorClient } from '../src/pdf-editor-client.js';

        let pdfEditor = null;
        let suggestions = [];

        // DOM元素
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const suggestionsList = document.getElementById('suggestionsList');
        const suggestionStats = document.getElementById('suggestionStats');
        const logArea = document.getElementById('logArea');

        // 初始化
        const iframe = document.getElementById('pdfViewer');
        iframe.addEventListener('load', async () => {
            try {
                log('正在初始化PDF编辑器...');
                pdfEditor = createPDFEditorClient(iframe);
                await pdfEditor.waitForReady();
                
                updateStatus('ready', 'PDF编辑器已就绪');
                log('PDF编辑器初始化成功');
                
                setupEventListeners();
                await refreshSuggestions();
                
            } catch (error) {
                updateStatus('error', '初始化失败: ' + error.message);
                log('ERROR: ' + error.message);
            }
        });

        // 设置事件监听器
        function setupEventListeners() {
            pdfEditor.on('suggestion-applied', (data) => {
                log(`建议已应用: ${data.suggestionId}`);
                refreshSuggestions();
            });

            pdfEditor.on('suggestion-rejected', (data) => {
                log(`建议已拒绝: ${data.suggestionId}`);
                refreshSuggestions();
            });
        }

        // 更新状态
        function updateStatus(status, text) {
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        // 日志记录
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 清除日志
        window.clearLog = function() {
            logArea.innerHTML = '';
        };

        // 加载审阅建议
        window.loadReviewSuggestions = async function() {
            const reviewSuggestions = [
                {
                    type: 'text-replace',
                    pageIndex: 0,
                    position: { x: 100, y: 300, width: 200, height: 15 },
                    originalText: 'performance',
                    newText: '性能表现',
                    description: '术语本地化 - 将performance翻译为性能表现',
                    priority: 'high',
                    category: 'review'
                },
                {
                    type: 'comment',
                    pageIndex: 0,
                    position: { x: 350, y: 250, width: 100, height: 20 },
                    newText: '建议添加更多技术细节',
                    description: '审阅意见 - 此处需要更详细的技术说明',
                    priority: 'normal',
                    category: 'review'
                },
                {
                    type: 'highlight',
                    pageIndex: 0,
                    position: { x: 150, y: 400, width: 180, height: 15 },
                    description: '重要内容标记 - 关键技术点',
                    priority: 'normal',
                    category: 'review'
                }
            ];

            try {
                const result = await pdfEditor.addSuggestions(reviewSuggestions);
                log(`已加载 ${result.suggestionIds.length} 个审阅建议`);
                await refreshSuggestions();
            } catch (error) {
                log('ERROR: 加载审阅建议失败 - ' + error.message);
            }
        };

        // 加载翻译建议
        window.loadTranslationSuggestions = async function() {
            const translationSuggestions = [
                {
                    type: 'text-replace',
                    pageIndex: 0,
                    position: { x: 200, y: 350, width: 120, height: 15 },
                    originalText: 'algorithm',
                    newText: '算法',
                    description: '翻译建议 - 将algorithm翻译为算法',
                    priority: 'high',
                    category: 'translation'
                },
                {
                    type: 'text-add',
                    pageIndex: 0,
                    position: { x: 400, y: 300, width: 0, height: 0 },
                    newText: '（译注：此处指的是动态编译技术）',
                    description: '翻译注释 - 添加术语解释',
                    priority: 'normal',
                    category: 'translation'
                }
            ];

            try {
                const result = await pdfEditor.addSuggestions(translationSuggestions);
                log(`已加载 ${result.suggestionIds.length} 个翻译建议`);
                await refreshSuggestions();
            } catch (error) {
                log('ERROR: 加载翻译建议失败 - ' + error.message);
            }
        };

        // 加载合规建议
        window.loadComplianceSuggestions = async function() {
            const complianceSuggestions = [
                {
                    type: 'text-add',
                    pageIndex: 0,
                    position: { x: 50, y: 500, width: 0, height: 0 },
                    newText: '⚠️ 重要提示：本文档包含技术敏感信息',
                    description: '合规要求 - 添加敏感信息提示',
                    priority: 'high',
                    category: 'compliance'
                },
                {
                    type: 'comment',
                    pageIndex: 0,
                    position: { x: 300, y: 450, width: 150, height: 20 },
                    newText: '需要法务部门审核',
                    description: '合规检查 - 标记需要法务审核的内容',
                    priority: 'high',
                    category: 'compliance'
                }
            ];

            try {
                const result = await pdfEditor.addSuggestions(complianceSuggestions);
                log(`已加载 ${result.suggestionIds.length} 个合规建议`);
                await refreshSuggestions();
            } catch (error) {
                log('ERROR: 加载合规建议失败 - ' + error.message);
            }
        };

        // 创建自定义建议
        window.createCustomSuggestion = async function() {
            try {
                const type = document.getElementById('suggestionType').value;
                const pageIndex = parseInt(document.getElementById('pageIndex').value);
                const positionStr = document.getElementById('position').value;
                const originalText = document.getElementById('originalText').value;
                const newText = document.getElementById('newText').value;
                const description = document.getElementById('description').value;

                const [x, y, width, height] = positionStr.split(',').map(v => parseFloat(v.trim()));

                const suggestion = {
                    type,
                    pageIndex,
                    position: { x, y, width, height },
                    originalText,
                    newText,
                    description,
                    priority: 'normal',
                    category: 'custom'
                };

                const result = await pdfEditor.addSuggestion(suggestion);
                log(`已创建自定义建议: ${result.suggestionId}`);
                await refreshSuggestions();

                // 清空表单
                document.getElementById('originalText').value = '';
                document.getElementById('newText').value = '';
                document.getElementById('description').value = '';

            } catch (error) {
                log('ERROR: 创建自定义建议失败 - ' + error.message);
            }
        };

        // 刷新建议列表
        window.refreshSuggestions = async function() {
            try {
                const result = await pdfEditor.getAllSuggestions();
                suggestions = result.suggestions;
                renderSuggestions();
                await updateStats();
                log(`已刷新建议列表，共 ${suggestions.length} 个建议`);
            } catch (error) {
                log('ERROR: 刷新建议列表失败 - ' + error.message);
            }
        };

        // 渲染建议列表
        function renderSuggestions() {
            suggestionsList.innerHTML = suggestions.map(suggestion => `
                <div class="suggestion-item ${suggestion.status}">
                    <div class="suggestion-info">
                        <strong>${getTypeText(suggestion.type)}</strong> - ${suggestion.description}
                        <br><small>页面: ${suggestion.pageIndex}, 优先级: ${suggestion.priority}, 状态: ${getStatusText(suggestion.status)}</small>
                    </div>
                    <div class="suggestion-actions">
                        ${suggestion.status === 'pending' ? `
                            <button class="btn btn-success" onclick="applySuggestion('${suggestion.id}')">应用</button>
                            <button class="btn btn-danger" onclick="rejectSuggestion('${suggestion.id}')">拒绝</button>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 应用建议
        window.applySuggestion = async function(suggestionId) {
            try {
                await pdfEditor.applySuggestion(suggestionId);
                log(`建议 ${suggestionId} 已应用`);
            } catch (error) {
                log(`ERROR: 应用建议失败 - ${error.message}`);
            }
        };

        // 拒绝建议
        window.rejectSuggestion = async function(suggestionId) {
            try {
                await pdfEditor.rejectSuggestion(suggestionId);
                log(`建议 ${suggestionId} 已拒绝`);
            } catch (error) {
                log(`ERROR: 拒绝建议失败 - ${error.message}`);
            }
        };

        // 应用所有建议
        window.applyAllSuggestions = async function() {
            const pendingSuggestions = suggestions.filter(s => s.status === 'pending');
            if (pendingSuggestions.length === 0) {
                log('没有待处理的建议');
                return;
            }

            try {
                const suggestionIds = pendingSuggestions.map(s => s.id);
                const results = await pdfEditor.applySuggestions(suggestionIds);
                const successCount = results.filter(r => r.success).length;
                log(`批量应用完成: ${successCount}/${results.length} 个建议成功应用`);
            } catch (error) {
                log(`ERROR: 批量应用失败 - ${error.message}`);
            }
        };

        // 更新统计信息
        async function updateStats() {
            try {
                const stats = await pdfEditor.getSuggestionsStatistics();
                suggestionStats.textContent = `总计: ${stats.total}, 待处理: ${stats.pending}, 已应用: ${stats.applied}, 已拒绝: ${stats.rejected}`;
            } catch (error) {
                log('ERROR: 更新统计信息失败 - ' + error.message);
            }
        }

        // 工具函数
        function getTypeText(type) {
            const typeMap = {
                'text-replace': '替换文本',
                'text-add': '添加文本',
                'text-delete': '删除文本',
                'highlight': '高亮',
                'comment': '评论'
            };
            return typeMap[type] || type;
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'applied': '已应用',
                'rejected': '已拒绝'
            };
            return statusMap[status] || status;
        }
    </script>
</body>
</html>
