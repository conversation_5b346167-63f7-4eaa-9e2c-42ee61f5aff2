<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF修改建议系统 - 示例</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-header {
            background: #2c3e50;
            color: white;
            padding: 15px;
            text-align: center;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .suggestion-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .suggestion-item.applied {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .suggestion-item.rejected {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .suggestion-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }

        .suggestion-type {
            background: #007acc;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .suggestion-type.text-replace { background: #28a745; }
        .suggestion-type.text-add { background: #17a2b8; }
        .suggestion-type.text-delete { background: #dc3545; }
        .suggestion-type.highlight { background: #ffc107; color: #000; }
        .suggestion-type.comment { background: #6f42c1; }

        .suggestion-priority {
            font-size: 11px;
            padding: 1px 6px;
            border-radius: 8px;
            margin-left: 5px;
        }

        .priority-high { background: #ff6b6b; color: white; }
        .priority-normal { background: #4ecdc4; color: white; }
        .priority-low { background: #95a5a6; color: white; }

        .suggestion-description {
            font-size: 14px;
            color: #495057;
            margin-bottom: 8px;
        }

        .suggestion-text {
            font-size: 12px;
            margin-bottom: 8px;
        }

        .original-text {
            background: #fff3cd;
            padding: 4px 8px;
            border-radius: 4px;
            margin-bottom: 4px;
        }

        .new-text {
            background: #d1ecf1;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .suggestion-actions {
            display: flex;
            gap: 5px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .btn-accept { background: #28a745; color: white; }
        .btn-accept:hover { background: #218838; }
        .btn-reject { background: #dc3545; color: white; }
        .btn-reject:hover { background: #c82333; }
        .btn-preview { background: #17a2b8; color: white; }
        .btn-preview:hover { background: #138496; }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background: #ecf0f1;
            padding: 10px;
            border-bottom: 1px solid #bdc3c7;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .pdf-container {
            flex: 1;
            border: none;
        }

        .status-bar {
            background: #34495e;
            color: white;
            padding: 8px 15px;
            font-size: 14px;
        }

        .stats {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .stats-item {
            display: inline-block;
            margin-right: 15px;
        }

        .filter-buttons {
            margin-bottom: 10px;
        }

        .filter-btn {
            padding: 4px 8px;
            margin-right: 5px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }

        .filter-btn.active {
            background: #007acc;
            color: white;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>修改建议</h3>
            <div id="statsDisplay" class="stats"></div>
        </div>
        
        <div class="sidebar-content">
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="pending">待处理</button>
                <button class="filter-btn" data-filter="applied">已应用</button>
                <button class="filter-btn" data-filter="rejected">已拒绝</button>
            </div>
            
            <div id="suggestionsList"></div>
        </div>
    </div>

    <div class="main-content">
        <div class="toolbar">
            <button id="btnLoadSampleSuggestions" class="btn btn-accept">加载示例建议</button>
            <button id="btnApplyAllPage" class="btn btn-accept">应用当前页全部</button>
            <button id="btnClearPreviews" class="btn btn-preview">清除预览</button>
            <button id="btnRefreshStats" class="btn btn-preview">刷新统计</button>
        </div>

        <iframe id="pdfViewer" class="pdf-container" 
                src="../web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf">
        </iframe>

        <div class="status-bar">
            <span id="statusText">正在加载PDF编辑器...</span>
        </div>
    </div>

    <script type="module">
        import { createPDFEditorClient } from '../src/pdf-editor-client.js';

        let pdfEditor = null;
        let currentFilter = 'all';
        let suggestions = [];

        // 初始化
        const iframe = document.getElementById('pdfViewer');
        const statusText = document.getElementById('statusText');
        const suggestionsList = document.getElementById('suggestionsList');
        const statsDisplay = document.getElementById('statsDisplay');

        iframe.addEventListener('load', async () => {
            try {
                pdfEditor = createPDFEditorClient(iframe);
                await pdfEditor.waitForReady();
                
                statusText.textContent = 'PDF编辑器已就绪';
                setupEventListeners();
                
            } catch (error) {
                console.error('初始化失败:', error);
                statusText.textContent = '初始化失败: ' + error.message;
            }
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 监听建议相关事件
            pdfEditor.on('suggestion-applied', (data) => {
                statusText.textContent = `建议 ${data.suggestionId} 已应用`;
                refreshSuggestions();
            });

            pdfEditor.on('suggestion-rejected', (data) => {
                statusText.textContent = `建议 ${data.suggestionId} 已拒绝`;
                refreshSuggestions();
            });

            // 工具栏按钮
            document.getElementById('btnLoadSampleSuggestions').addEventListener('click', loadSampleSuggestions);
            document.getElementById('btnApplyAllPage').addEventListener('click', applyAllPageSuggestions);
            document.getElementById('btnClearPreviews').addEventListener('click', clearAllPreviews);
            document.getElementById('btnRefreshStats').addEventListener('click', refreshStats);

            // 过滤按钮
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    currentFilter = e.target.dataset.filter;
                    renderSuggestions();
                });
            });
        }

        // 加载示例建议
        async function loadSampleSuggestions() {
            const sampleSuggestions = [
                {
                    type: 'text-replace',
                    pageIndex: 0,
                    position: { x: 100, y: 200, width: 150, height: 20 },
                    originalText: 'original text',
                    newText: '修改后的文本',
                    description: '将英文替换为中文',
                    priority: 'high',
                    category: 'translation'
                },
                {
                    type: 'text-add',
                    pageIndex: 0,
                    position: { x: 300, y: 250, width: 0, height: 0 },
                    newText: '新增的重要说明',
                    description: '添加重要说明文字',
                    priority: 'normal',
                    category: 'content'
                },
                {
                    type: 'highlight',
                    pageIndex: 0,
                    position: { x: 150, y: 300, width: 200, height: 15 },
                    description: '高亮重要段落',
                    priority: 'normal',
                    category: 'emphasis'
                },
                {
                    type: 'comment',
                    pageIndex: 0,
                    position: { x: 400, y: 350, width: 100, height: 20 },
                    newText: '这里需要进一步说明',
                    description: '添加评论说明',
                    priority: 'low',
                    category: 'review'
                }
            ];

            try {
                const result = await pdfEditor.addSuggestions(sampleSuggestions);
                statusText.textContent = `已加载 ${result.suggestionIds.length} 个示例建议`;
                await refreshSuggestions();
            } catch (error) {
                statusText.textContent = '加载示例建议失败: ' + error.message;
            }
        }

        // 刷新建议列表
        async function refreshSuggestions() {
            try {
                const result = await pdfEditor.getAllSuggestions();
                suggestions = result.suggestions;
                renderSuggestions();
                await refreshStats();
            } catch (error) {
                console.error('刷新建议列表失败:', error);
            }
        }

        // 渲染建议列表
        function renderSuggestions() {
            const filteredSuggestions = suggestions.filter(s => {
                if (currentFilter === 'all') return true;
                return s.status === currentFilter;
            });

            suggestionsList.innerHTML = filteredSuggestions.map(suggestion => `
                <div class="suggestion-item ${suggestion.status}" data-id="${suggestion.id}">
                    <div class="suggestion-header">
                        <span class="suggestion-type ${suggestion.type}">${getTypeText(suggestion.type)}</span>
                        <span class="suggestion-priority priority-${suggestion.priority}">${suggestion.priority}</span>
                    </div>
                    <div class="suggestion-description">${suggestion.description}</div>
                    ${suggestion.originalText ? `<div class="suggestion-text">
                        <div class="original-text">原文: ${suggestion.originalText}</div>
                    </div>` : ''}
                    ${suggestion.newText ? `<div class="suggestion-text">
                        <div class="new-text">新文: ${suggestion.newText}</div>
                    </div>` : ''}
                    <div class="suggestion-actions">
                        ${suggestion.status === 'pending' ? `
                            <button class="btn btn-accept" onclick="applySuggestion('${suggestion.id}')">接受</button>
                            <button class="btn btn-reject" onclick="rejectSuggestion('${suggestion.id}')">拒绝</button>
                            <button class="btn btn-preview" onclick="previewSuggestion('${suggestion.id}')">预览</button>
                        ` : `
                            <span style="color: #6c757d; font-size: 12px;">状态: ${getStatusText(suggestion.status)}</span>
                        `}
                    </div>
                </div>
            `).join('');
        }

        // 应用建议
        window.applySuggestion = async function(suggestionId) {
            try {
                await pdfEditor.applySuggestion(suggestionId);
                statusText.textContent = `建议 ${suggestionId} 已应用`;
                await refreshSuggestions();
            } catch (error) {
                statusText.textContent = '应用建议失败: ' + error.message;
            }
        };

        // 拒绝建议
        window.rejectSuggestion = async function(suggestionId) {
            try {
                await pdfEditor.rejectSuggestion(suggestionId);
                statusText.textContent = `建议 ${suggestionId} 已拒绝`;
                await refreshSuggestions();
            } catch (error) {
                statusText.textContent = '拒绝建议失败: ' + error.message;
            }
        };

        // 预览建议
        window.previewSuggestion = async function(suggestionId) {
            try {
                await pdfEditor.previewSuggestion(suggestionId);
                statusText.textContent = `正在预览建议 ${suggestionId}`;
            } catch (error) {
                statusText.textContent = '预览建议失败: ' + error.message;
            }
        };

        // 应用当前页所有建议
        async function applyAllPageSuggestions() {
            try {
                const result = await pdfEditor.applyPageSuggestions(0); // 应用第一页的建议
                statusText.textContent = `已应用当前页的 ${result.length} 个建议`;
                await refreshSuggestions();
            } catch (error) {
                statusText.textContent = '批量应用失败: ' + error.message;
            }
        }

        // 清除所有预览
        async function clearAllPreviews() {
            // 这里需要遍历所有建议并清除预览
            statusText.textContent = '已清除所有预览';
        }

        // 刷新统计信息
        async function refreshStats() {
            try {
                const stats = await pdfEditor.getSuggestionsStatistics();
                statsDisplay.innerHTML = `
                    <div class="stats-item">总计: ${stats.total}</div>
                    <div class="stats-item">待处理: ${stats.pending}</div>
                    <div class="stats-item">已应用: ${stats.applied}</div>
                    <div class="stats-item">已拒绝: ${stats.rejected}</div>
                `;
            } catch (error) {
                console.error('刷新统计失败:', error);
            }
        }

        // 工具函数
        function getTypeText(type) {
            const typeMap = {
                'text-replace': '替换文本',
                'text-add': '添加文本',
                'text-delete': '删除文本',
                'highlight': '高亮',
                'comment': '评论',
                'stamp': '图章'
            };
            return typeMap[type] || type;
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'applied': '已应用',
                'rejected': '已拒绝'
            };
            return statusMap[status] || status;
        }
    </script>
</body>
</html>
