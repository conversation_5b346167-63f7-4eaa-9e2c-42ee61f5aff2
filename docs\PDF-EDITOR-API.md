# PDF.js 编辑器扩展 API 文档

## 概述

PDF.js 编辑器扩展提供了一套完整的 PDF 在线编辑解决方案，支持通过 iframe 进行跨域通信和编辑操作。该扩展基于 PDF.js 的原生编辑功能，提供了更丰富的 API 接口和更好的缩放适配。

## 特性

- ✅ 支持文本注释编辑
- ✅ 支持绘图/墨迹注释
- ✅ 支持高亮注释
- ✅ 支持图章注释
- ✅ 支持签名注释
- ✅ 完整的撤销/重做功能
- ✅ 页面缩放自适应
- ✅ iframe 跨域通信
- ✅ 坐标转换支持
- ✅ PDF 保存和导出

## 快速开始

### 1. 构建项目

```bash
# 安装依赖
npm install

# 构建通用版本
npm run gulp generic

# 构建完成后，文件将位于 build/generic/ 目录
```

### 2. 基本使用

```html
<!DOCTYPE html>
<html>
<head>
    <title>PDF编辑器示例</title>
</head>
<body>
    <!-- PDF查看器iframe -->
    <iframe id="pdfViewer" 
            src="build/generic/web/viewer.html?file=your-document.pdf"
            width="100%" height="600px">
    </iframe>

    <script type="module">
        import { createPDFEditorClient } from 'build/generic/src/pdf-editor-client.js';

        const iframe = document.getElementById('pdfViewer');
        const pdfEditor = createPDFEditorClient(iframe);

        // 等待编辑器准备就绪
        await pdfEditor.waitForReady();

        // 启用文本编辑模式
        await pdfEditor.enableTextMode();

        // 添加文本注释
        await pdfEditor.addTextAnnotation({
            x: 100,
            y: 100,
            pageIndex: 0,
            text: 'Hello World!',
            fontSize: 14,
            color: [0, 0, 0]
        });
    </script>
</body>
</html>
```

## API 参考

### PDFEditorClient

#### 构造函数

```javascript
const pdfEditor = new PDFEditorClient(iframe);
// 或使用工厂函数
const pdfEditor = createPDFEditorClient(iframe);
```

**参数:**
- `iframe` (HTMLIFrameElement): PDF.js viewer 的 iframe 元素

#### 方法

##### waitForReady()

等待编辑器准备就绪。

```javascript
await pdfEditor.waitForReady();
```

**返回:** `Promise<void>`

##### 编辑模式控制

```javascript
// 启用文本编辑模式
await pdfEditor.enableTextMode();

// 启用绘图模式
await pdfEditor.enableInkMode();

// 启用高亮模式
await pdfEditor.enableHighlightMode();

// 启用图章模式
await pdfEditor.enableStampMode();

// 禁用编辑模式
await pdfEditor.disableEditMode();

// 自定义模式切换
await pdfEditor.switchEditorMode(mode, options);
```

**参数:**
- `mode` (number): 编辑器模式常量
- `options` (object): 可选参数
  - `editId` (string): 要编辑的注释ID
  - `isFromKeyboard` (boolean): 是否来自键盘操作
  - `mustEnterInEditMode` (boolean): 是否必须进入编辑模式
  - `editComment` (boolean): 是否编辑评论

##### 添加注释

###### addTextAnnotation(options)

添加文本注释。

```javascript
const result = await pdfEditor.addTextAnnotation({
    x: 100,                    // X坐标
    y: 100,                    // Y坐标
    pageIndex: 0,              // 页面索引（从0开始）
    text: 'Hello World!',      // 文本内容
    fontSize: 14,              // 字体大小
    color: [0, 0, 0]          // RGB颜色数组
});
```

**返回:** `Promise<{success: boolean, editorId: string, annotationId: string}>`

###### addInkAnnotation(options)

添加绘图/墨迹注释。

```javascript
const result = await pdfEditor.addInkAnnotation({
    pageIndex: 0,              // 页面索引
    paths: [                   // 绘图路径数组
        [{x: 10, y: 10}, {x: 20, y: 20}]
    ],
    color: [0, 0, 0],         // RGB颜色
    thickness: 2,              // 线条粗细
    opacity: 1                 // 透明度
});
```

###### addHighlightAnnotation(options)

添加高亮注释。

```javascript
const result = await pdfEditor.addHighlightAnnotation({
    pageIndex: 0,              // 页面索引
    quadPoints: [              // 高亮区域四边形点
        [x1, y1, x2, y2, x3, y3, x4, y4]
    ],
    color: [255, 255, 0],     // RGB颜色
    opacity: 0.3               // 透明度
});
```

###### addStampAnnotation(options)

添加图章注释。

```javascript
const result = await pdfEditor.addStampAnnotation({
    x: 100,                    // X坐标
    y: 100,                    // Y坐标
    pageIndex: 0,              // 页面索引
    imageUrl: 'stamp.png',     // 图片URL
    width: 100,                // 宽度
    height: 100                // 高度
});
```

##### 注释管理

```javascript
// 删除注释
await pdfEditor.removeAnnotation(editorId);

// 获取所有注释
const result = await pdfEditor.getAllAnnotations();
console.log(result.annotations);

// 设置编辑器属性
await pdfEditor.setEditorProperties(editorId, {
    fontSize: 16,
    color: [255, 0, 0]
});
```

##### 操作控制

```javascript
// 撤销
await pdfEditor.undo();

// 重做
await pdfEditor.redo();

// 保存PDF
const result = await pdfEditor.savePDF();
const blob = new Blob([result.data], { type: 'application/pdf' });
```

##### 信息获取

```javascript
// 获取支持的编辑模式
const modes = pdfEditor.getSupportedModes();

// 获取当前编辑模式
const currentMode = pdfEditor.getCurrentMode();
```

#### 事件

##### 监听事件

```javascript
// 编辑器模式变化
pdfEditor.on('editor-mode-changed', (data) => {
    console.log('模式变化:', data.mode);
});

// 编辑器状态变化
pdfEditor.on('editor-states-changed', (data) => {
    console.log('状态变化:', data);
});

// 缩放变化
pdfEditor.on('scale-changing', (data) => {
    console.log('缩放变化:', data.scale);
});

// 一次性监听
pdfEditor.once('editor-initialized', (data) => {
    console.log('编辑器已初始化');
});

// 移除监听
pdfEditor.off('editor-mode-changed', callback);
```

## 编辑器模式常量

```javascript
const AnnotationEditorType = {
    DISABLE: -1,    // 禁用
    NONE: 0,        // 无编辑
    FREETEXT: 3,    // 文本编辑
    HIGHLIGHT: 9,   // 高亮
    STAMP: 13,      // 图章
    INK: 15,        // 绘图/墨迹
    SIGNATURE: 101  // 签名
};
```

## 坐标系统

PDF.js 使用 PDF 坐标系统，原点在页面左下角。编辑器扩展提供了坐标转换功能：

```javascript
// 屏幕坐标转PDF坐标
const pdfCoords = await pdfEditor.screenToPDFCoordinates(screenX, screenY, pageNumber);

// PDF坐标转屏幕坐标
const screenCoords = await pdfEditor.pdfToScreenCoordinates(pdfX, pdfY, pageNumber);

// 获取缩放信息
const scaleInfo = await pdfEditor.getScaleInfo();
```

## 缩放适配

编辑器扩展自动处理页面缩放，确保编辑功能在不同缩放级别下正常工作：

- 自动调整编辑器元素位置和大小
- 自动调整字体大小和线条粗细
- 提供坐标转换API
- 监听缩放变化事件

## 错误处理

所有异步方法都会抛出错误，建议使用 try-catch 进行错误处理：

```javascript
try {
    await pdfEditor.addTextAnnotation({
        x: 100,
        y: 100,
        pageIndex: 0,
        text: 'Hello World!'
    });
} catch (error) {
    console.error('添加文本注释失败:', error.message);
}
```

## 浏览器兼容性

- Chrome 110+
- Firefox ESR
- Safari 16.4+
- Edge 110+

## 注意事项

1. 确保 iframe 的 src 指向正确的 viewer.html 路径
2. 编辑器扩展需要在 iframe 加载完成后初始化
3. 跨域通信需要正确配置 CORS 策略
4. 保存的 PDF 包含所有编辑内容
5. 编辑操作支持撤销/重做
6. 缩放变化时编辑器会自动适配

## 示例项目

完整的示例项目位于 `examples/pdf-editor-iframe-example.html`，展示了所有主要功能的使用方法。
