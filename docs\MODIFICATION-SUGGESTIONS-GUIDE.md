# PDF修改建议系统使用指南

## 概述

PDF修改建议系统允许外部系统（如审阅系统、翻译系统、合规检查系统等）向PDF文档提供修改建议，用户可以在建议列表中点击"接受"按钮，PDF原文立即显示修改效果。

## 核心特性

### 🎯 一键应用修改
- 用户点击"接受"按钮
- PDF页面立即显示修改效果
- 支持文本替换、添加、删除、高亮、评论等多种修改类型

### 📋 建议管理
- 支持批量添加建议
- 按页面、类型、优先级分类管理
- 实时统计和状态跟踪

### 🔄 实时预览
- 预览修改效果而不实际应用
- 支持撤销和重做操作
- 保持PDF文档的完整性

## 实现架构

```
外部系统 → PDF编辑器客户端 → iframe通信 → PDF.js扩展 → 修改建议管理器 → PDF渲染
```

### 核心组件

1. **PDFModificationSuggestions**: 修改建议管理器
2. **PDFEditorClient**: 客户端通信库
3. **PDFEditorExtension**: 编辑器扩展核心

## 使用流程

### 1. 初始化编辑器

```javascript
import { createPDFEditorClient } from 'path/to/pdf-editor-client.js';

const iframe = document.getElementById('pdfViewer');
const pdfEditor = createPDFEditorClient(iframe);
await pdfEditor.waitForReady();
```

### 2. 添加修改建议

```javascript
// 单个建议
const suggestion = {
    type: 'text-replace',
    pageIndex: 0,
    position: { x: 100, y: 200, width: 150, height: 20 },
    originalText: '原始文本',
    newText: '修改后的文本',
    description: '修改说明',
    priority: 'high',
    category: 'review'
};

const result = await pdfEditor.addSuggestion(suggestion);
console.log('建议ID:', result.suggestionId);

// 批量建议
const suggestions = [suggestion1, suggestion2, suggestion3];
const batchResult = await pdfEditor.addSuggestions(suggestions);
```

### 3. 构建建议列表UI

```html
<div id="suggestionsList">
    <!-- 建议项将通过JavaScript动态生成 -->
</div>
```

```javascript
// 获取所有建议
const allSuggestions = await pdfEditor.getAllSuggestions();

// 渲染建议列表
function renderSuggestions(suggestions) {
    const listElement = document.getElementById('suggestionsList');
    listElement.innerHTML = suggestions.map(suggestion => `
        <div class="suggestion-item" data-id="${suggestion.id}">
            <div class="suggestion-info">
                <h4>${suggestion.description}</h4>
                <p>类型: ${suggestion.type} | 优先级: ${suggestion.priority}</p>
                ${suggestion.originalText ? `<p>原文: ${suggestion.originalText}</p>` : ''}
                ${suggestion.newText ? `<p>新文: ${suggestion.newText}</p>` : ''}
            </div>
            <div class="suggestion-actions">
                <button onclick="acceptSuggestion('${suggestion.id}')">接受</button>
                <button onclick="rejectSuggestion('${suggestion.id}')">拒绝</button>
                <button onclick="previewSuggestion('${suggestion.id}')">预览</button>
            </div>
        </div>
    `).join('');
}
```

### 4. 实现一键应用功能

```javascript
// 接受建议 - 关键功能
async function acceptSuggestion(suggestionId) {
    try {
        // 应用建议，PDF立即显示修改效果
        await pdfEditor.applySuggestion(suggestionId);
        
        // 更新UI状态
        updateSuggestionStatus(suggestionId, 'applied');
        
        // 显示成功消息
        showMessage('修改已应用到PDF文档');
        
        // 刷新建议列表
        await refreshSuggestionsList();
        
    } catch (error) {
        showError('应用修改失败: ' + error.message);
    }
}

// 拒绝建议
async function rejectSuggestion(suggestionId) {
    try {
        await pdfEditor.rejectSuggestion(suggestionId);
        updateSuggestionStatus(suggestionId, 'rejected');
        showMessage('建议已拒绝');
        await refreshSuggestionsList();
    } catch (error) {
        showError('拒绝建议失败: ' + error.message);
    }
}

// 预览建议
async function previewSuggestion(suggestionId) {
    try {
        await pdfEditor.previewSuggestion(suggestionId);
        showMessage('正在预览修改效果');
    } catch (error) {
        showError('预览失败: ' + error.message);
    }
}
```

### 5. 批量操作

```javascript
// 应用页面所有建议
async function applyAllPageSuggestions(pageIndex) {
    try {
        const results = await pdfEditor.applyPageSuggestions(pageIndex);
        const successCount = results.filter(r => r.success).length;
        showMessage(`已应用 ${successCount} 个建议`);
        await refreshSuggestionsList();
    } catch (error) {
        showError('批量应用失败: ' + error.message);
    }
}

// 应用选中的建议
async function applySelectedSuggestions(suggestionIds) {
    try {
        const results = await pdfEditor.applySuggestions(suggestionIds);
        const successCount = results.filter(r => r.success).length;
        showMessage(`批量应用完成: ${successCount}/${results.length}`);
        await refreshSuggestionsList();
    } catch (error) {
        showError('批量应用失败: ' + error.message);
    }
}
```

## 建议类型详解

### 1. 文本替换 (text-replace)
```javascript
{
    type: 'text-replace',
    pageIndex: 0,
    position: { x: 100, y: 200, width: 150, height: 20 },
    originalText: '原始文本',
    newText: '替换文本',
    description: '将原始文本替换为新文本'
}
```
**效果**: 原文本区域显示红色高亮（表示删除），附近显示绿色新文本

### 2. 文本添加 (text-add)
```javascript
{
    type: 'text-add',
    pageIndex: 0,
    position: { x: 300, y: 250, width: 0, height: 0 },
    newText: '新增文本内容',
    description: '在指定位置添加文本'
}
```
**效果**: 在指定位置显示绿色新文本

### 3. 文本删除 (text-delete)
```javascript
{
    type: 'text-delete',
    pageIndex: 0,
    position: { x: 150, y: 300, width: 200, height: 15 },
    originalText: '要删除的文本',
    description: '删除指定文本'
}
```
**效果**: 文本区域显示红色高亮和删除线

### 4. 高亮标记 (highlight)
```javascript
{
    type: 'highlight',
    pageIndex: 0,
    position: { x: 200, y: 350, width: 180, height: 15 },
    description: '高亮重要内容'
}
```
**效果**: 文本区域显示黄色高亮背景

### 5. 添加评论 (comment)
```javascript
{
    type: 'comment',
    pageIndex: 0,
    position: { x: 400, y: 400, width: 100, height: 20 },
    newText: '这是一条评论',
    description: '添加评论说明'
}
```
**效果**: 在指定位置显示蓝色评论文本

## 事件监听

```javascript
// 监听建议应用事件
pdfEditor.on('suggestion-applied', (data) => {
    console.log('建议已应用:', data.suggestionId);
    // 更新UI状态
    updateSuggestionInList(data.suggestionId, 'applied');
});

// 监听建议拒绝事件
pdfEditor.on('suggestion-rejected', (data) => {
    console.log('建议已拒绝:', data.suggestionId);
    updateSuggestionInList(data.suggestionId, 'rejected');
});

// 监听建议预览事件
pdfEditor.on('suggestion-previewed', (data) => {
    console.log('建议预览中:', data.suggestionId);
});
```

## 最佳实践

### 1. 用户体验优化
- 提供清晰的建议描述
- 使用不同颜色区分建议类型
- 显示建议的优先级和分类
- 提供批量操作功能

### 2. 性能优化
- 分页加载大量建议
- 使用虚拟滚动处理长列表
- 缓存建议数据减少重复请求

### 3. 错误处理
- 捕获并显示友好的错误消息
- 提供重试机制
- 记录操作日志便于调试

### 4. 状态管理
- 实时同步建议状态
- 提供撤销功能
- 保存用户操作历史

## 集成示例

完整的集成示例请参考：
- `examples/pdf-modification-suggestions-example.html` - 基础功能演示
- `examples/pdf-suggestions-api-demo.html` - API使用演示

## 常见问题

### Q: 如何确保修改建议的坐标准确？
A: 使用PDF坐标系统，原点在页面左下角。可以使用坐标转换API进行屏幕坐标和PDF坐标的转换。

### Q: 建议应用后能否撤销？
A: 是的，所有修改操作都支持撤销和重做功能。

### Q: 如何处理大量建议的性能问题？
A: 建议使用分页加载、虚拟滚动等技术，避免一次性渲染过多DOM元素。

### Q: 建议数据如何持久化？
A: 建议数据存储在内存中，如需持久化可以通过API获取所有建议数据并保存到后端。
