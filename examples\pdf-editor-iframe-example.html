<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF编辑器 - iframe集成示例</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .toolbar {
            background: #ecf0f1;
            padding: 15px;
            border-bottom: 1px solid #bdc3c7;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .pdf-container {
            height: 800px;
            border: none;
            width: 100%;
        }

        .status {
            background: #34495e;
            color: white;
            padding: 10px 20px;
            font-size: 14px;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .input-group input {
            padding: 6px 10px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }

        .separator {
            width: 1px;
            height: 30px;
            background: #bdc3c7;
            margin: 0 10px;
        }

        .mode-indicator {
            background: #95a5a6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .mode-indicator.active {
            background: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PDF在线编辑器</h1>
            <p>基于PDF.js的iframe集成方案，支持文本、绘图、高亮、图章等编辑功能</p>
        </div>

        <div class="toolbar">
            <!-- 编辑模式切换 -->
            <div class="input-group">
                <span>编辑模式:</span>
                <span id="modeIndicator" class="mode-indicator">无编辑</span>
            </div>
            
            <div class="separator"></div>

            <!-- 编辑工具 -->
            <button id="btnTextMode" class="btn btn-primary">文本编辑</button>
            <button id="btnInkMode" class="btn btn-primary">绘图</button>
            <button id="btnHighlightMode" class="btn btn-primary">高亮</button>
            <button id="btnStampMode" class="btn btn-primary">图章</button>
            <button id="btnDisableEdit" class="btn btn-warning">禁用编辑</button>

            <div class="separator"></div>

            <!-- 操作工具 -->
            <button id="btnUndo" class="btn btn-success">撤销</button>
            <button id="btnRedo" class="btn btn-success">重做</button>
            <button id="btnSave" class="btn btn-success">保存PDF</button>

            <div class="separator"></div>

            <!-- 快速添加注释 -->
            <div class="input-group">
                <input type="text" id="textInput" placeholder="输入文本内容" style="width: 150px;">
                <button id="btnAddText" class="btn btn-primary">添加文本</button>
            </div>

            <div class="separator"></div>

            <!-- 信息显示 -->
            <button id="btnGetAnnotations" class="btn btn-warning">获取注释</button>
            <button id="btnGetScale" class="btn btn-warning">获取缩放</button>
        </div>

        <iframe id="pdfViewer" class="pdf-container"
                src="../web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf">
        </iframe>

        <div class="status">
            <span>状态: </span>
            <span id="statusText">正在加载PDF编辑器...</span>
        </div>
    </div>

    <!-- 引入PDF编辑器客户端 -->
    <script type="module">
        import { createPDFEditorClient } from '../src/pdf-editor-client.js';

        // 获取DOM元素
        const iframe = document.getElementById('pdfViewer');
        const statusText = document.getElementById('statusText');
        const modeIndicator = document.getElementById('modeIndicator');
        const textInput = document.getElementById('textInput');

        // 创建PDF编辑器客户端
        let pdfEditor = null;

        // 等待iframe加载完成
        iframe.addEventListener('load', async () => {
            try {
                pdfEditor = createPDFEditorClient(iframe);
                
                // 等待编辑器准备就绪
                await pdfEditor.waitForReady();
                
                statusText.textContent = 'PDF编辑器已就绪';
                
                // 设置事件监听器
                setupEventListeners();
                
                // 启用按钮
                enableButtons();
                
            } catch (error) {
                console.error('初始化PDF编辑器失败:', error);
                statusText.textContent = '初始化失败: ' + error.message;
            }
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 监听编辑器模式变化
            pdfEditor.on('editor-mode-changed', (data) => {
                updateModeIndicator(data.mode);
                statusText.textContent = `编辑模式已切换: ${getModeText(data.mode)}`;
            });

            // 监听编辑器状态变化
            pdfEditor.on('editor-states-changed', (data) => {
                console.log('编辑器状态变化:', data);
            });

            // 监听缩放变化
            pdfEditor.on('scale-changing', (data) => {
                statusText.textContent = `缩放比例: ${(data.scale * 100).toFixed(0)}%`;
            });
        }

        // 启用按钮功能
        function enableButtons() {
            // 编辑模式切换
            document.getElementById('btnTextMode').addEventListener('click', async () => {
                try {
                    await pdfEditor.enableTextMode();
                    statusText.textContent = '已切换到文本编辑模式';
                } catch (error) {
                    statusText.textContent = '切换失败: ' + error.message;
                }
            });

            document.getElementById('btnInkMode').addEventListener('click', async () => {
                try {
                    await pdfEditor.enableInkMode();
                    statusText.textContent = '已切换到绘图模式';
                } catch (error) {
                    statusText.textContent = '切换失败: ' + error.message;
                }
            });

            document.getElementById('btnHighlightMode').addEventListener('click', async () => {
                try {
                    await pdfEditor.enableHighlightMode();
                    statusText.textContent = '已切换到高亮模式';
                } catch (error) {
                    statusText.textContent = '切换失败: ' + error.message;
                }
            });

            document.getElementById('btnStampMode').addEventListener('click', async () => {
                try {
                    await pdfEditor.enableStampMode();
                    statusText.textContent = '已切换到图章模式';
                } catch (error) {
                    statusText.textContent = '切换失败: ' + error.message;
                }
            });

            document.getElementById('btnDisableEdit').addEventListener('click', async () => {
                try {
                    await pdfEditor.disableEditMode();
                    statusText.textContent = '已禁用编辑模式';
                } catch (error) {
                    statusText.textContent = '操作失败: ' + error.message;
                }
            });

            // 操作工具
            document.getElementById('btnUndo').addEventListener('click', async () => {
                try {
                    await pdfEditor.undo();
                    statusText.textContent = '已撤销上一步操作';
                } catch (error) {
                    statusText.textContent = '撤销失败: ' + error.message;
                }
            });

            document.getElementById('btnRedo').addEventListener('click', async () => {
                try {
                    await pdfEditor.redo();
                    statusText.textContent = '已重做操作';
                } catch (error) {
                    statusText.textContent = '重做失败: ' + error.message;
                }
            });

            document.getElementById('btnSave').addEventListener('click', async () => {
                try {
                    statusText.textContent = '正在保存PDF...';
                    const result = await pdfEditor.savePDF();
                    
                    // 创建下载链接
                    const blob = new Blob([result.data], { type: 'application/pdf' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'edited-document.pdf';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    statusText.textContent = 'PDF已保存并下载';
                } catch (error) {
                    statusText.textContent = '保存失败: ' + error.message;
                }
            });

            // 快速添加文本
            document.getElementById('btnAddText').addEventListener('click', async () => {
                const text = textInput.value.trim();
                if (!text) {
                    statusText.textContent = '请输入文本内容';
                    return;
                }

                try {
                    const result = await pdfEditor.addTextAnnotation({
                        x: 100,
                        y: 100,
                        pageIndex: 0,
                        text: text,
                        fontSize: 14,
                        color: [0, 0, 0]
                    });
                    
                    statusText.textContent = `已添加文本注释: ${result.editorId}`;
                    textInput.value = '';
                } catch (error) {
                    statusText.textContent = '添加文本失败: ' + error.message;
                }
            });

            // 信息获取
            document.getElementById('btnGetAnnotations').addEventListener('click', async () => {
                try {
                    const result = await pdfEditor.getAllAnnotations();
                    console.log('所有注释:', result.annotations);
                    statusText.textContent = `共有 ${result.annotations.length} 个注释`;
                } catch (error) {
                    statusText.textContent = '获取注释失败: ' + error.message;
                }
            });

            document.getElementById('btnGetScale').addEventListener('click', async () => {
                try {
                    const currentMode = pdfEditor.getCurrentMode();
                    const supportedModes = pdfEditor.getSupportedModes();
                    console.log('当前模式:', currentMode);
                    console.log('支持的模式:', supportedModes);
                    statusText.textContent = `当前模式: ${getModeText(currentMode)}`;
                } catch (error) {
                    statusText.textContent = '获取信息失败: ' + error.message;
                }
            });
        }

        // 更新模式指示器
        function updateModeIndicator(mode) {
            modeIndicator.textContent = getModeText(mode);
            modeIndicator.className = mode === 0 ? 'mode-indicator' : 'mode-indicator active';
        }

        // 获取模式文本
        function getModeText(mode) {
            const modeMap = {
                0: '无编辑',
                3: '文本编辑',
                9: '高亮',
                13: '图章',
                15: '绘图',
                101: '签名'
            };
            return modeMap[mode] || '未知模式';
        }
    </script>
</body>
</html>
