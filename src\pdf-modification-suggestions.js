/* Copyright 2024 PDF.js Modification Suggestions
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { AnnotationEditorType } from "./shared/util.js";

/**
 * PDF修改建议管理器
 * 处理修改建议的应用、预览和管理
 */
class PDFModificationSuggestions {
  #pdfEditor = null;
  #suggestions = new Map();
  #appliedSuggestions = new Set();
  #previewMode = false;
  #previewElements = new Map();

  constructor(pdfEditor) {
    this.#pdfEditor = pdfEditor;
  }

  /**
   * 添加修改建议
   * @param {Object} suggestion - 修改建议对象
   */
  addSuggestion(suggestion) {
    const id = suggestion.id || this.#generateId();
    const suggestionData = {
      id,
      type: suggestion.type, // 'text-replace', 'text-add', 'text-delete', 'highlight', 'comment'
      pageIndex: suggestion.pageIndex,
      position: suggestion.position, // {x, y, width, height}
      originalText: suggestion.originalText || '',
      newText: suggestion.newText || '',
      description: suggestion.description || '',
      priority: suggestion.priority || 'normal', // 'high', 'normal', 'low'
      category: suggestion.category || 'general',
      timestamp: Date.now(),
      status: 'pending' // 'pending', 'accepted', 'rejected', 'applied'
    };

    this.#suggestions.set(id, suggestionData);
    return id;
  }

  /**
   * 批量添加修改建议
   */
  addSuggestions(suggestions) {
    const ids = [];
    suggestions.forEach(suggestion => {
      const id = this.addSuggestion(suggestion);
      ids.push(id);
    });
    return ids;
  }

  /**
   * 获取所有建议
   */
  getAllSuggestions() {
    return Array.from(this.#suggestions.values());
  }

  /**
   * 获取指定页面的建议
   */
  getSuggestionsByPage(pageIndex) {
    return Array.from(this.#suggestions.values())
      .filter(suggestion => suggestion.pageIndex === pageIndex);
  }

  /**
   * 获取指定类型的建议
   */
  getSuggestionsByType(type) {
    return Array.from(this.#suggestions.values())
      .filter(suggestion => suggestion.type === type);
  }

  /**
   * 应用单个修改建议
   */
  async applySuggestion(suggestionId) {
    const suggestion = this.#suggestions.get(suggestionId);
    if (!suggestion) {
      throw new Error(`Suggestion ${suggestionId} not found`);
    }

    if (this.#appliedSuggestions.has(suggestionId)) {
      console.warn(`Suggestion ${suggestionId} already applied`);
      return;
    }

    try {
      await this.#executeSuggestion(suggestion);
      
      // 更新状态
      suggestion.status = 'applied';
      this.#appliedSuggestions.add(suggestionId);
      
      // 触发事件
      this.#dispatchEvent('suggestion-applied', {
        suggestionId,
        suggestion
      });

      return {
        success: true,
        suggestionId,
        type: suggestion.type
      };
    } catch (error) {
      console.error(`Failed to apply suggestion ${suggestionId}:`, error);
      throw error;
    }
  }

  /**
   * 批量应用修改建议
   */
  async applySuggestions(suggestionIds) {
    const results = [];
    
    for (const id of suggestionIds) {
      try {
        const result = await this.applySuggestion(id);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          suggestionId: id,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * 应用页面的所有建议
   */
  async applyPageSuggestions(pageIndex) {
    const pageSuggestions = this.getSuggestionsByPage(pageIndex);
    const suggestionIds = pageSuggestions.map(s => s.id);
    return await this.applySuggestions(suggestionIds);
  }

  /**
   * 执行具体的修改建议
   */
  async #executeSuggestion(suggestion) {
    const { type, pageIndex, position, originalText, newText } = suggestion;

    switch (type) {
      case 'text-replace':
        await this.#replaceText(pageIndex, position, originalText, newText);
        break;
        
      case 'text-add':
        await this.#addText(pageIndex, position, newText);
        break;
        
      case 'text-delete':
        await this.#deleteText(pageIndex, position, originalText);
        break;
        
      case 'highlight':
        await this.#addHighlight(pageIndex, position, suggestion.color);
        break;
        
      case 'comment':
        await this.#addComment(pageIndex, position, newText);
        break;
        
      case 'stamp':
        await this.#addStamp(pageIndex, position, suggestion.stampData);
        break;
        
      default:
        throw new Error(`Unknown suggestion type: ${type}`);
    }
  }

  /**
   * 替换文本
   */
  async #replaceText(pageIndex, position, originalText, newText) {
    // 首先删除原文本区域（通过添加删除线或覆盖）
    await this.#pdfEditor.addHighlightAnnotation({
      pageIndex,
      quadPoints: [this.#positionToQuadPoints(position)],
      color: [255, 0, 0], // 红色表示删除
      opacity: 0.3
    });

    // 然后在附近添加新文本
    await this.#pdfEditor.addTextAnnotation({
      x: position.x,
      y: position.y - 20, // 稍微偏移
      pageIndex,
      text: newText,
      fontSize: 12,
      color: [0, 128, 0] // 绿色表示新增
    });
  }

  /**
   * 添加文本
   */
  async #addText(pageIndex, position, text) {
    await this.#pdfEditor.addTextAnnotation({
      x: position.x,
      y: position.y,
      pageIndex,
      text,
      fontSize: 12,
      color: [0, 128, 0] // 绿色表示新增
    });
  }

  /**
   * 删除文本（通过添加删除线）
   */
  async #deleteText(pageIndex, position, text) {
    await this.#pdfEditor.addHighlightAnnotation({
      pageIndex,
      quadPoints: [this.#positionToQuadPoints(position)],
      color: [255, 0, 0], // 红色表示删除
      opacity: 0.5
    });

    // 添加删除线效果
    await this.#pdfEditor.addInkAnnotation({
      pageIndex,
      paths: [
        [
          { x: position.x, y: position.y + position.height / 2 },
          { x: position.x + position.width, y: position.y + position.height / 2 }
        ]
      ],
      color: [255, 0, 0],
      thickness: 2
    });
  }

  /**
   * 添加高亮
   */
  async #addHighlight(pageIndex, position, color = [255, 255, 0]) {
    await this.#pdfEditor.addHighlightAnnotation({
      pageIndex,
      quadPoints: [this.#positionToQuadPoints(position)],
      color,
      opacity: 0.3
    });
  }

  /**
   * 添加评论
   */
  async #addComment(pageIndex, position, comment) {
    await this.#pdfEditor.addTextAnnotation({
      x: position.x + position.width + 10,
      y: position.y,
      pageIndex,
      text: `💬 ${comment}`,
      fontSize: 10,
      color: [0, 0, 255] // 蓝色表示评论
    });
  }

  /**
   * 添加图章
   */
  async #addStamp(pageIndex, position, stampData) {
    await this.#pdfEditor.addStampAnnotation({
      x: position.x,
      y: position.y,
      pageIndex,
      imageUrl: stampData.imageUrl,
      width: stampData.width || 50,
      height: stampData.height || 50
    });
  }

  /**
   * 预览建议效果
   */
  async previewSuggestion(suggestionId) {
    const suggestion = this.#suggestions.get(suggestionId);
    if (!suggestion) {
      throw new Error(`Suggestion ${suggestionId} not found`);
    }

    // 创建预览元素
    const previewElement = await this.#createPreviewElement(suggestion);
    this.#previewElements.set(suggestionId, previewElement);

    this.#dispatchEvent('suggestion-previewed', {
      suggestionId,
      suggestion
    });
  }

  /**
   * 清除预览
   */
  clearPreview(suggestionId) {
    const previewElement = this.#previewElements.get(suggestionId);
    if (previewElement) {
      previewElement.remove();
      this.#previewElements.delete(suggestionId);
    }
  }

  /**
   * 清除所有预览
   */
  clearAllPreviews() {
    this.#previewElements.forEach((element, id) => {
      element.remove();
    });
    this.#previewElements.clear();
  }

  /**
   * 拒绝建议
   */
  rejectSuggestion(suggestionId) {
    const suggestion = this.#suggestions.get(suggestionId);
    if (suggestion) {
      suggestion.status = 'rejected';
      this.#dispatchEvent('suggestion-rejected', {
        suggestionId,
        suggestion
      });
    }
  }

  /**
   * 删除建议
   */
  removeSuggestion(suggestionId) {
    const suggestion = this.#suggestions.get(suggestionId);
    if (suggestion) {
      this.clearPreview(suggestionId);
      this.#suggestions.delete(suggestionId);
      this.#appliedSuggestions.delete(suggestionId);
      
      this.#dispatchEvent('suggestion-removed', {
        suggestionId,
        suggestion
      });
    }
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const all = this.getAllSuggestions();
    return {
      total: all.length,
      pending: all.filter(s => s.status === 'pending').length,
      applied: all.filter(s => s.status === 'applied').length,
      rejected: all.filter(s => s.status === 'rejected').length,
      byType: this.#groupBy(all, 'type'),
      byPage: this.#groupBy(all, 'pageIndex'),
      byPriority: this.#groupBy(all, 'priority')
    };
  }

  /**
   * 工具方法
   */
  #generateId() {
    return `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  #positionToQuadPoints(position) {
    const { x, y, width, height } = position;
    return [
      x, y + height,           // 左下
      x + width, y + height,   // 右下
      x + width, y,            // 右上
      x, y                     // 左上
    ];
  }

  #groupBy(array, key) {
    return array.reduce((groups, item) => {
      const group = item[key];
      groups[group] = (groups[group] || 0) + 1;
      return groups;
    }, {});
  }

  #createPreviewElement(suggestion) {
    // 创建预览DOM元素的逻辑
    const element = document.createElement('div');
    element.className = 'suggestion-preview';
    element.style.cssText = `
      position: absolute;
      border: 2px dashed #007acc;
      background: rgba(0, 122, 204, 0.1);
      pointer-events: none;
      z-index: 1000;
    `;
    
    // 根据建议类型设置样式和内容
    // 这里需要根据实际的PDF页面坐标系进行定位
    
    return element;
  }

  #dispatchEvent(eventType, data) {
    if (typeof window !== 'undefined' && window.parent !== window) {
      window.parent.postMessage({
        type: `suggestion-${eventType}`,
        data
      }, '*');
    }
  }
}

export { PDFModificationSuggestions };
